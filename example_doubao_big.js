/**
 * 豆包大模型 ASR 服务使用示例
 * 
 * 这个示例展示了如何使用新的豆包大模型 ASR 服务
 */

const { AsrClient } = require('./asr_doubao_big');

async function exampleUsage() {
  console.log('=== 豆包大模型 ASR 服务示例 ===\n');

  // 1. 创建 ASR 客户端
  console.log('1. 创建 ASR 客户端...');
  const asrClient = new AsrClient('zh'); // 中文识别
  
  try {
    // 2. 启动服务
    console.log('2. 启动 ASR 服务...');
    const started = await asrClient.start();
    if (!started) {
      throw new Error('ASR 服务启动失败');
    }
    console.log('✓ ASR 服务启动成功\n');

    // 3. 模拟音频数据
    console.log('3. 准备音频数据...');
    // 创建一些模拟的 PCM 音频数据 (16kHz, 16bit, mono)
    const sampleRate = 16000;
    const duration = 2; // 2秒
    const audioData = Buffer.alloc(sampleRate * 2 * duration); // 16bit = 2 bytes
    
    // 填充一些简单的音频数据（这里只是示例，实际使用时应该是真实的音频数据）
    for (let i = 0; i < audioData.length; i += 2) {
      // 生成简单的正弦波
      const sample = Math.sin(2 * Math.PI * 440 * (i / 2) / sampleRate) * 0.1;
      const intSample = Math.floor(sample * 32767);
      audioData.writeInt16LE(intSample, i);
    }
    
    console.log(`✓ 准备了 ${duration} 秒的音频数据 (${audioData.length} 字节)\n`);

    // 4. 发送音频数据进行识别
    console.log('4. 发送音频数据进行识别...');
    let resultReceived = false;
    
    await asrClient.requestAsr(audioData, (result) => {
      console.log('📝 收到 ASR 结果:');
      console.log(`   - 是否最终结果: ${result.isFinal}`);
      console.log(`   - 识别文本: "${result.transcript}"`);
      console.log('');
      resultReceived = true;
    }, 'zh');

    // 5. 结束识别会话
    console.log('5. 结束识别会话...');
    await asrClient.end();
    console.log('✓ 识别会话已结束\n');

    console.log('=== 示例完成 ===');
    
    if (!resultReceived) {
      console.log('⚠️  注意: 由于使用的是模拟音频数据，可能不会产生有意义的识别结果');
    }

  } catch (error) {
    console.error('❌ 示例执行失败:', error.message);
    console.error('错误详情:', error);
  } finally {
    // 6. 清理资源
    console.log('6. 清理资源...');
    asrClient.close();
    console.log('✓ 资源清理完成');
  }
}

// WebSocket 客户端示例
async function webSocketClientExample() {
  console.log('\n=== WebSocket 客户端示例 ===\n');
  
  const WebSocket = require('ws');
  
  // 连接到本地服务器
  const ws = new WebSocket('ws://localhost:8001');
  
  ws.on('open', () => {
    console.log('✓ 连接到 WebSocket 服务器');
    
    // 发送配置消息，选择豆包大模型
    const config = {
      asr_language: 'zh',
      translate_language: 'en',
      asr_model: 10,  // 豆包大模型
      translate_model: 0,
      tts: false,
      tts_model: 0,
      sessionId: 'test-session-' + Date.now()
    };
    
    console.log('📤 发送配置:', config);
    ws.send(JSON.stringify(config));
    
    // 模拟发送音频数据
    setTimeout(() => {
      console.log('📤 发送模拟音频数据...');
      const audioData = Buffer.alloc(1600, 0); // 100ms 的静音数据
      ws.send(audioData);
      
      // 发送结束标记
      setTimeout(() => {
        console.log('📤 发送结束标记...');
        ws.send(JSON.stringify({ end: true }));
      }, 1000);
    }, 500);
  });
  
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      console.log('📥 收到消息:', message);
    } catch (e) {
      console.log('📥 收到二进制数据:', data.length, '字节');
    }
  });
  
  ws.on('close', () => {
    console.log('🔌 WebSocket 连接已关闭');
  });
  
  ws.on('error', (error) => {
    console.error('❌ WebSocket 错误:', error.message);
  });
}

// 主函数
async function main() {
  console.log('豆包大模型 ASR 服务使用示例\n');
  console.log('选择运行模式:');
  console.log('1. 直接使用 AsrClient (默认)');
  console.log('2. WebSocket 客户端示例');
  
  const args = process.argv.slice(2);
  const mode = args[0] || '1';
  
  if (mode === '2') {
    console.log('\n启动 WebSocket 客户端示例...');
    console.log('请确保 server.js 正在运行 (node server.js)');
    await webSocketClientExample();
  } else {
    console.log('\n启动直接使用示例...');
    await exampleUsage();
  }
}

// 运行示例
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  exampleUsage,
  webSocketClientExample
};
