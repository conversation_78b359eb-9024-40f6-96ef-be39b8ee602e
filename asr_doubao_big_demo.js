// sauc_ws_pcm_nostream.js
// 使用：npm init -y && npm i ws uuid
// 运行示例：
//   node sauc_ws_pcm_nostream.js --file ./test.pcm --rate 16000 --bits 16 --ch 1 \
//     --segms 200 --pace 200 \
//     --app_key YOUR_APP_KEY --access_key YOUR_ACCESS_KEY \
//     --url wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_nostream

const fs = require('fs')
const { gzipSync, gunzipSync } = require('zlib')
const WebSocket = require('ws')
const { v4: uuidv4 } = require('uuid')
const path = require('path')

const DefaultURL =
  'wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_nostream'

// ---- 协议枚举（与 Go 版一致） ----
const ProtocolV1 = 0b0001

const MsgClientFullRequest = 0b0001
const MsgClientAudioOnly = 0b0010
const MsgServerFullResponse = 0b1001
const MsgServerErrorResponse = 0b1111

const FlagNoSeq = 0b0000
const FlagPosSeq = 0b0001
const FlagNegSeq = 0b0010
const FlagNegWithSeq = 0b0011

const SerNone = 0b0000
const SerJSON = 0b0001

const CompGzip = 0b0001

const HeaderLenWords = 0x01 // 4 bytes
const ReservedByte = 0x00

const AuthResourceID = 'volc.bigasr.sauc.duration'

let buffer = ''

// ---- 简单参数解析 ----
function arg(key, def = undefined) {
  const i = process.argv.indexOf(key)
  if (i >= 0 && i + 1 < process.argv.length) return process.argv[i + 1]
  return def
}

const filePath = arg('--file')
const url = arg('--url', DefaultURL)
const appKey = arg('--app_key', process.env.VOLC_APP_KEY || '8359578722')
const accessKey = arg(
  '--access_key',
  process.env.VOLC_ACCESS_KEY || 'Zol8wcj36NJGQC141tLZgNkvdVg-o_a7'
)

const rate = parseInt(arg('--rate', '16000'), 10)
const bits = parseInt(arg('--bits', '16'), 10)
const ch = parseInt(arg('--ch', '1'), 10)

const segMS = parseInt(arg('--segms', '200'), 10)
const pace = parseInt(arg('--pace', '200'), 10)

if (!filePath) {
  console.error('--file 是必须的（原始 PCM s16le）')
  process.exit(1)
}
if (!appKey || !accessKey) {
  console.error(
    '--app_key / --access_key 必填，或设 VOLC_APP_KEY / VOLC_ACCESS_KEY'
  )
  process.exit(1)
}
if (!url.includes('bigmodel_nostream')) {
  console.error('只允许 bigmodel_nostream 模式')
  process.exit(1)
}
if (bits % 8 !== 0) {
  console.error('bits 必须是 8 的倍数')
  process.exit(1)
}

const abs = path.resolve(filePath)
console.log('PCM file:', abs)

let raw
try {
  raw = fs.readFileSync(filePath)
} catch (e) {
  console.error('读取文件失败：', e.message)
  process.exit(1)
}

const bytesPerSample = bits / 8
const bytesPerSec = rate * bytesPerSample * ch
const segBytes = Math.floor((bytesPerSec * segMS) / 1000)
if (segBytes <= 0) {
  console.error('segBytes 计算无效，请检查 rate/bits/ch/segms')
  process.exit(1)
}
if (raw.length % (bytesPerSample * ch) !== 0) {
  console.warn(
    `警告：文件长度(${raw.length})不是一帧(${
      bytesPerSample * ch
    })的整数倍，可能不是期望的 PCM 格式`
  )
}

// ---- 工具 ----
function buildHeader(messageType, flags, serialization, compression) {
  const h = Buffer.alloc(4)
  h[0] = ((ProtocolV1 << 4) | (HeaderLenWords & 0x0f)) & 0xff
  h[1] = (((messageType & 0x0f) << 4) | (flags & 0x0f)) & 0xff
  h[2] = (((serialization & 0x0f) << 4) | (compression & 0x0f)) & 0xff
  h[3] = ReservedByte
  return h
}

function writeI32BE(v) {
  const b = Buffer.alloc(4)
  b.writeInt32BE(v, 0)
  return b
}
function writeU32BE(v) {
  const b = Buffer.alloc(4)
  b.writeUInt32BE(v >>> 0, 0)
  return b
}

function newFullClientRequest(seq, rate, bits, ch) {
  const payload = {
    user: { uid: 'demo_uid' },
    audio: {
      format: 'pcm', // 关键：原始 PCM
      codec: 'raw',
      rate: rate,
      bits: bits,
      channel: ch,
    },
    request: {
      model_name: 'bigmodel',
      enable_itn: true,
      enable_punc: true,
      enable_ddc: true,
      show_utterances: true,
      enable_nonstream: false, // nostream
      end_window_size: 300,
    },
  }
  const js = Buffer.from(JSON.stringify(payload), 'utf8')
  const gz = gzipSync(js)
  const hdr = buildHeader(MsgClientFullRequest, FlagPosSeq, SerJSON, CompGzip)
  return Buffer.concat([hdr, writeI32BE(seq), writeU32BE(gz.length), gz])
}

function newAudioOnlyRequest(seq, segment, isLast) {
  const flags = isLast ? FlagNegWithSeq : FlagPosSeq
  const txSeq = isLast ? -seq : seq
  const gz = gzipSync(segment)
  const hdr = buildHeader(MsgClientAudioOnly, flags, SerJSON, CompGzip)
  return Buffer.concat([hdr, writeI32BE(txSeq), writeU32BE(gz.length), gz])
}

function splitBytes(buf, chunk) {
  if (chunk <= 0 || chunk >= buf.length) return [buf]
  const out = []
  for (let i = 0; i < buf.length; i += chunk) {
    out.push(buf.slice(i, Math.min(i + chunk, buf.length)))
  }
  return out
}

function parseResponse(buf) {
  if (buf.length < 4) throw new Error('resp too short')

  const headerWords = buf[0] & 0x0f
  const typ = (buf[1] >> 4) & 0x0f
  const flags = buf[1] & 0x0f
  const ser = (buf[2] >> 4) & 0x0f
  const comp = buf[2] & 0x0f

  let offset = headerWords * 4
  if (offset > buf.length) throw new Error('bad header size')
  let payload = buf.slice(offset)

  const res = {
    code: 0,
    event: 0,
    is_last_package: false,
    payload_sequence: 0,
    payload_size: 0,
    payload_msg: null,
    raw_json: null,
  }

  // flags
  if (flags & 0x01) {
    if (payload.length < 4) throw new Error('missing seq')
    res.payload_sequence = payload.readInt32BE(0)
    payload = payload.slice(4)
  }
  if (flags & 0x02) {
    res.is_last_package = true
  }
  if (flags & 0x04) {
    if (payload.length < 4) throw new Error('missing event')
    res.event = payload.readInt32BE(0)
    payload = payload.slice(4)
  }

  // type
  if (typ === MsgServerFullResponse) {
    if (payload.length < 4) throw new Error('missing payload size')
    res.payload_size = payload.readUInt32BE(0)
    payload = payload.slice(4)
  } else if (typ === MsgServerErrorResponse) {
    if (payload.length < 8) throw new Error('missing error payload')
    res.code = payload.readInt32BE(0)
    res.payload_size = payload.readUInt32BE(4)
    payload = payload.slice(8)
  }

  // 解压 & JSON
  if (comp === CompGzip && payload.length > 0) {
    try {
      payload = gunzipSync(payload)
    } catch (_) {
      /* ignore */
    }
  }
  if (ser === SerJSON && payload.length > 0) {
    res.raw_json = payload.toString('utf8')
    try {
      res.payload_msg = JSON.parse(res.raw_json)
    } catch (_) {}
  }
  return res
}

function sleep(ms) {
  return new Promise((r) => setTimeout(r, ms))
}

// ---- 主流程 ----
;(async () => {
  console.log(`connected to: ${url}`)
  let seq = 1

  const headers = {
    'X-Api-Resource-Id': AuthResourceID,
    'X-Api-Request-Id': uuidv4(),
    'X-Api-Access-Key': accessKey,
    'X-Api-App-Key': appKey,
  }

  const ws = new WebSocket(url, { headers })

  let firstResponseSeen = false
  let closed = false

  ws.on('open', () => {
    const full = newFullClientRequest(seq, rate, bits, ch)
    ws.send(full)
    console.log(`FULL_REQUEST sent seq=${seq}`)
    seq++
  })

  ws.on('message', async (data, isBinary) => {
    if (!isBinary) return

    let r
    try {
      r = parseResponse(Buffer.from(data))
    } catch (e) {
      console.error('parse response error:', e.message)
      return
    }

    if (!firstResponseSeen) {
      firstResponseSeen = true
      console.log(
        'first response:\n' + JSON.stringify(r.payload_msg ?? r, null, 2)
      )

      // 开始发送音频
      const chunks = splitBytes(raw, segBytes)
      if (chunks.length === 0) {
        console.error('no audio data')
        ws.close()
        return
      }

      ;(async () => {
        for (let i = 0; i < chunks.length; i++) {
          const isLast = i === chunks.length - 1
          const req = newAudioOnlyRequest(seq, chunks[i], isLast)
          ws.send(req)
          if (!isLast) seq++
          await sleep(pace)
        }
      })().catch((err) => console.error('send loop error:', err))
      return
    }

    // 常规接收
    const pretty = r.raw_json ? JSON.parse(r.raw_json) : r
    if (pretty.result.text && buffer !== pretty.result.text) {
      buffer = pretty.result.text

      // 找到pretty.result.utterances数组中最后一个definite为true的值
      const lastDefiniteUtterance = pretty.result.utterances.findLast(
        (utterance) => utterance.definite
      )

      console.log('识别的完整文本: ' + lastDefiniteUtterance.text)
    }

    if (r.is_last_package || r.code !== 0) {
      // 正常结束或服务端报错
      if (!closed) {
        closed = true
        try {
          ws.close(1000)
        } catch (_) {}
      }
    }
  })

  ws.on('error', (err) => {
    console.error('ws error:', err.message)
  })

  ws.on('close', (code, reason) => {
    console.log('ws closed:', code, reason?.toString() || '')
    process.exit(0)
  })

  // Ctrl+C
  process.on('SIGINT', () => {
    try {
      ws.close(1000)
    } catch (_) {}
  })
})()
