const WebSocket = require('ws')
const fs = require('fs')
// const ws = new WebSocket('wss://iyuuki.xyz/ws/')
const ws = new WebSocket('ws://127.0.0.1:8001')

ws.onmessage = async (event) => {
  console.log(event.data)
  const js = JSON.parse(event.data)

}

ws.once('open', async () => {
  ws.send(
    JSON.stringify({
      asr_language: 'zh',
      translate_language: 'en',
      asr_model: 10,
      translate_model: 2,
    })
  )

  const filename = `./long_wav.wav`
  const readStream = fs.createReadStream(filename, {
    highWaterMark: 1024 * 2,
  })
  for await (const chunk of readStream) {
    ws.send(chunk)
    await new Promise((resolve) => setTimeout(resolve, 40))
  }
})
