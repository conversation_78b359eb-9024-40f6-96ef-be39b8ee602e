# 豆包大模型 ASR 服务 (Node.js 版本)

这是将 Python 版本的 `asr_doubao_big_python.py` 改造成的 Node.js 版本，专门使用 `bigmodel_nostream` 模式。

## 功能特点

- 🎯 **专注于 bigmodel_nostream 模式**：只使用豆包大模型的非流式模式
- 🔄 **统一接口**：与其他 ASR 服务（如 asr_keda_realtime.js）保持相同的接口
- 📦 **易于集成**：可以直接在 server.js 中使用
- 🎵 **音频处理**：直接处理 PCM 音频数据，无需文件转换
- 🔧 **错误处理**：完善的错误处理和资源清理

## 文件结构

```
asr_doubao_big.js          # 主要的 ASR 客户端实现
test_doubao_big.js         # 测试文件
README_doubao_big.md       # 本说明文档
```

## 主要类和功能

### AsrClient 类
主要的 ASR 客户端类，提供与其他 ASR 服务相同的接口：

```javascript
const { AsrClient } = require('./asr_doubao_big');

const asrClient = new AsrClient('zh'); // 支持语言：zh, en, ja, ko 等

// 启动服务
await asrClient.start();

// 发送音频数据
await asrClient.requestAsr(audioData, (result) => {
  console.log('ASR 结果:', result);
}, 'zh');

// 结束会话
await asrClient.end();
```

### 其他工具类
- `AsrWsClient`: WebSocket 客户端实现
- `CommonUtils`: 音频处理工具
- `RequestBuilder`: 请求构建器
- `ResponseParser`: 响应解析器

## 在 server.js 中的集成

已经在 server.js 中添加了对豆包大模型的支持：

1. **导入模块**：
```javascript
const { AsrClient: AsrClientDoubaoBig } = require('./asr_doubao_big')
```

2. **模型配置**：
- ASR 模型 ID: `10` (豆包大模型)
- 模型名称: "豆包大模型"

3. **初始化**：
```javascript
else if (asrModel == 10) {
  asrClient = new AsrClientDoubaoBig(asrLanguage)
}
```

## 配置说明

当前使用的配置：
```javascript
const config = {
  auth: {
    app_key: "8359578722",
    access_key: "Zol8wcj36NJGQC141tLZgNkvdVg-o_a7"
  }
};
```

WebSocket 端点：
```
wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_nostream
```

## 使用方式

### 1. 在客户端选择 ASR 模型
在前端或客户端中，将 `asr_model` 设置为 `10` 来使用豆包大模型：

```javascript
const message = {
  asr_language: 'zh',
  translate_language: 'en', 
  asr_model: 10,  // 豆包大模型
  translate_model: 0,
  tts: false,
  tts_model: 0
};
```

### 2. 发送音频数据
发送 PCM 音频数据（16kHz, 16bit, 单声道）到 WebSocket 服务器。

### 3. 接收识别结果
服务器会返回识别结果：
```javascript
{
  type: 'asr',
  isFinal: true,
  transcript: '识别到的文字内容'
}
```

## 技术特点

### 音频处理
- 直接处理 PCM 音频数据
- 自动生成 WAV 头部信息
- 无需外部依赖（如 FFmpeg）

### 协议实现
- 完整的豆包 ASR 协议实现
- 支持 gzip 压缩
- 正确的消息头和序列号处理

### 错误处理
- WebSocket 连接错误处理
- 音频处理错误处理
- 临时文件自动清理

## 依赖要求

- Node.js 环境
- 以下 npm 包：
  - `ws` (WebSocket 客户端)
  - `uuid` (生成唯一 ID)
  - `zlib` (内置，用于 gzip 压缩)

## 测试

运行测试：
```bash
node test_doubao_big.js
```

## 注意事项

1. **非流式模式**：此实现专门针对 `bigmodel_nostream` 模式，会累积音频数据后一次性处理
2. **PCM 数据**：直接处理 PCM 音频数据，无需文件操作
3. **音频格式**：输入的 PCM 数据会自动添加 WAV 头部，默认为 16kHz, 16bit, 单声道
4. **API 密钥**：请确保使用有效的豆包 API 密钥

## 与原 Python 版本的差异

1. **语言**：从 Python 改为 Node.js
2. **接口统一**：与其他 ASR 服务保持相同的接口
3. **集成方式**：直接集成到现有的 server.js 中
4. **音频处理**：从文件操作改为直接处理 PCM 数据流
5. **依赖简化**：移除了 FFmpeg 依赖，无需外部工具
6. **错误处理**：增强了错误处理和资源管理
7. **模式专一**：只支持 bigmodel_nostream 模式
