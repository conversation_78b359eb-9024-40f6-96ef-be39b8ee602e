const { AsrClient } = require('./asr_doubao_big');

async function testDoubaoBigASR() {
  console.log('开始测试豆包大模型 ASR 服务...');

  const asrClient = new AsrClient('zh');

  // 创建一个简单的测试音频数据 (模拟PCM数据)
  // 16kHz, 16bit, mono, 2秒的数据
  const sampleRate = 16000;
  const duration = 2; // 2秒
  const testAudioData = Buffer.alloc(sampleRate * 2 * duration); // 16bit = 2 bytes

  // 填充一些简单的音频数据（正弦波）
  for (let i = 0; i < testAudioData.length; i += 2) {
    const sample = Math.sin(2 * Math.PI * 440 * (i / 2) / sampleRate) * 0.1;
    const intSample = Math.floor(sample * 32767);
    testAudioData.writeInt16LE(intSample, i);
  }

  try {
    console.log('初始化 ASR 客户端...');
    await asrClient.start();

    console.log('发送测试音频数据...');
    await asrClient.requestAsr(testAudioData, (result) => {
      console.log('收到 ASR 结果:', result);
    }, 'zh');

    console.log('结束 ASR 会话...');
    await asrClient.end();

    console.log('测试完成');
  } catch (error) {
    console.error('测试失败:', error.message);
  } finally {
    asrClient.close();
  }
}

// 运行测试
if (require.main === module) {
  testDoubaoBigASR().catch(console.error);
}

module.exports = { testDoubaoBigASR };
